
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import DigitalRadiography from "./pages/DigitalRadiography";
import ComputedTomography from "./pages/ComputedTomography";
import Ultrasound from "./pages/Ultrasound";
import MonitoringSolutions from "./pages/MonitoringSolutions";
import ECG from "./pages/ECG";
import AboutUs from "./pages/AboutUs";
import Contact from "./pages/Contact";
import WomensHealth from "./pages/WomensHealth";
import PointOfCare from "./pages/PointOfCare";
import Cardiology from "./pages/Cardiology";
import Resources from "./pages/Resources";
import Education from "./pages/Education";
import Video from "./pages/Video";
import Insight from "./pages/Insight";
import Download from "./pages/Download";
import CleaningGuidelines from "./pages/CleaningGuidelines";
import CatalogueSoftware from "./pages/CatalogueSoftware";
import Manual from "./pages/Manual";
import Service from "./pages/Service";
import ServiceOfferings from "./pages/ServiceOfferings";
import RMS from "./pages/RMS";
import SitePlanning from "./pages/SitePlanning";
import ContactUs from "./pages/ContactUs";
import Inquiry from "./pages/Inquiry";
import GlobalNetwork from "./pages/GlobalNetwork";
import MedtronicHealthcare from "./pages/MedtronicHealthcare";
import NewsCenter from "./pages/NewsCenter";
import Events from "./pages/Events";
import Sustainability from "./pages/Sustainability";
import CyberSecurity from "./pages/CyberSecurity";
import ValueUpPackage from "./pages/ValueUpPackage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/ultrasound" element={<Ultrasound />} />
          <Route path="/digital-radiography" element={<DigitalRadiography />} />
          <Route path="/computed-tomography" element={<ComputedTomography />} />
          <Route path="/monitoring-solutions" element={<MonitoringSolutions />} />
          <Route path="/ecg" element={<ECG />} />
          <Route path="/womens-health" element={<WomensHealth />} />
          <Route path="/point-of-care" element={<PointOfCare />} />
          <Route path="/cardiology" element={<Cardiology />} />
          <Route path="/about-us" element={<AboutUs />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/resources" element={<Resources />} />
          <Route path="/education" element={<Education />} />
          <Route path="/video" element={<Video />} />
          <Route path="/insight" element={<Insight />} />
          <Route path="/download" element={<Download />} />
          <Route path="/cleaning-guidelines" element={<CleaningGuidelines />} />
          <Route path="/catalogue-software" element={<CatalogueSoftware />} />
          <Route path="/manual" element={<Manual />} />
          <Route path="/service" element={<Service />} />
          <Route path="/service-offerings" element={<ServiceOfferings />} />
          <Route path="/rms" element={<RMS />} />
          <Route path="/site-planning" element={<SitePlanning />} />
          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/inquiry" element={<Inquiry />} />
          <Route path="/global-network" element={<GlobalNetwork />} />
          <Route path="/medtronic-healthcare" element={<MedtronicHealthcare />} />
          <Route path="/news-center" element={<NewsCenter />} />
          <Route path="/events" element={<Events />} />
          <Route path="/sustainability" element={<Sustainability />} />
          <Route path="/cyber-security" element={<CyberSecurity />} />
          <Route path="/value-up-package" element={<ValueUpPackage />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
