
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const NewsCenter = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">News Center</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">Latest News & Updates</h2>
              
              <div className="space-y-6">
                <article className="border-l-4 border-blue-500 pl-6 py-4">
                  <h3 className="text-xl font-medium text-gray-900 mb-2">
                    New Ultrasound Technology Arrives in Nepal
                  </h3>
                  <p className="text-gray-600 mb-2">
                    Medtronic Healthcare introduces advanced ultrasound systems to improve diagnostic capabilities across the country.
                  </p>
                  <span className="text-sm text-gray-500">January 15, 2024</span>
                </article>
                
                <article className="border-l-4 border-green-500 pl-6 py-4">
                  <h3 className="text-xl font-medium text-gray-900 mb-2">
                    Partnership with Leading Hospitals
                  </h3>
                  <p className="text-gray-600 mb-2">
                    Expanding our network of healthcare partners to bring better medical equipment access nationwide.
                  </p>
                  <span className="text-sm text-gray-500">December 20, 2023</span>
                </article>
                
                <article className="border-l-4 border-purple-500 pl-6 py-4">
                  <h3 className="text-xl font-medium text-gray-900 mb-2">
                    Training Program Launch
                  </h3>
                  <p className="text-gray-600 mb-2">
                    New comprehensive training programs for healthcare professionals on latest medical technologies.
                  </p>
                  <span className="text-sm text-gray-500">November 30, 2023</span>
                </article>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default NewsCenter;
