
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Leaf, Recycle, Globe } from 'lucide-react';

const Sustainability = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Sustainability</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Our Commitment to Sustainability</h2>
              <p className="text-gray-600 mb-6">
                At Medtronic Healthcare, we are committed to creating a sustainable future through 
                responsible business practices and environmental stewardship.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-green-50 p-6 rounded-lg text-center">
                  <Leaf className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Environmental Responsibility</h3>
                  <p className="text-gray-600">
                    Reducing our carbon footprint through eco-friendly practices and sustainable technologies.
                  </p>
                </div>
                
                <div className="bg-blue-50 p-6 rounded-lg text-center">
                  <Recycle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Circular Economy</h3>
                  <p className="text-gray-600">
                    Implementing recycling programs and sustainable product lifecycle management.
                  </p>
                </div>
                
                <div className="bg-purple-50 p-6 rounded-lg text-center">
                  <Globe className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Global Impact</h3>
                  <p className="text-gray-600">
                    Contributing to global sustainability goals while serving local healthcare needs.
                  </p>
                </div>
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Sustainability Initiatives</h2>
              <div className="space-y-4">
                <div className="border-l-4 border-green-500 pl-4">
                  <h3 className="font-medium text-gray-900">Energy Efficiency</h3>
                  <p className="text-gray-600">Developing energy-efficient medical equipment to reduce power consumption.</p>
                </div>
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="font-medium text-gray-900">Waste Reduction</h3>
                  <p className="text-gray-600">Minimizing packaging waste and promoting equipment refurbishment programs.</p>
                </div>
                <div className="border-l-4 border-purple-500 pl-4">
                  <h3 className="font-medium text-gray-900">Sustainable Supply Chain</h3>
                  <p className="text-gray-600">Partnering with suppliers who share our commitment to environmental responsibility.</p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Sustainability;
