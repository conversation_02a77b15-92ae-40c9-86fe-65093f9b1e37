
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';

const Inquiry = () => {
  const navigate = useNavigate();

  const handleInquiryClick = (inquiryType: string) => {
    // Navigate to contact page with optional query parameter to indicate inquiry type
    navigate('/contact', { state: { inquiryType } });
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Submit an Inquiry</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">How Can We Help You?</h2>
              <p className="text-gray-600 mb-6">
                Choose the type of inquiry that best describes your needs, and we'll connect you 
                with the right specialist to assist you.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <CardTitle>Product Information</CardTitle>
                    <CardDescription>
                      Learn more about our medical imaging solutions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-2 mb-4">
                      <li>• Product specifications and features</li>
                      <li>• Pricing and availability</li>
                      <li>• Technical documentation</li>
                      <li>• Compatibility requirements</li>
                    </ul>
                    <Button
                      className="w-full"
                      onClick={() => handleInquiryClick('Product Information')}
                    >
                      Get Product Info
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <CardTitle>Service Request</CardTitle>
                    <CardDescription>
                      Request service support for your equipment
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-2 mb-4">
                      <li>• Maintenance and repairs</li>
                      <li>• Technical support</li>
                      <li>• Training programs</li>
                      <li>• Warranty information</li>
                    </ul>
                    <Button
                      className="w-full"
                      onClick={() => handleInquiryClick('Service Request')}
                    >
                      Request Service
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <CardTitle>Sales Inquiry</CardTitle>
                    <CardDescription>
                      Connect with our sales team for purchasing
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-2 mb-4">
                      <li>• Equipment purchasing</li>
                      <li>• Leasing options</li>
                      <li>• Custom solutions</li>
                      <li>• Volume discounts</li>
                    </ul>
                    <Button
                      className="w-full"
                      onClick={() => handleInquiryClick('Sales Inquiry')}
                    >
                      Contact Sales
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <CardTitle>Partnership Opportunities</CardTitle>
                    <CardDescription>
                      Explore business partnership possibilities
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-2 mb-4">
                      <li>• Distribution partnerships</li>
                      <li>• Technology collaborations</li>
                      <li>• Research partnerships</li>
                      <li>• Strategic alliances</li>
                    </ul>
                    <Button
                      className="w-full"
                      onClick={() => handleInquiryClick('Partnership Opportunities')}
                    >
                      Explore Partnerships
                    </Button>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-8 bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Quick Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-900">Emergency Support</p>
                    <p className="text-gray-600">+1 (800) 633-8766</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">General Inquiries</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Response Time</p>
                    <p className="text-gray-600">Within 24 hours</p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Inquiry;
