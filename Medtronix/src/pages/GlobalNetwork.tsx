
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const GlobalNetwork = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Global Network</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Worldwide Service Coverage</h2>
              <p className="text-gray-600 mb-6">
                Our global network spans across continents, providing local support with international 
                expertise to ensure your medical equipment operates optimally wherever you are.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">North America</CardTitle>
                    <CardDescription>
                      Comprehensive coverage across the United States, Canada, and Mexico
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 50+ service centers</li>
                      <li>• 200+ certified technicians</li>
                      <li>• 24/7 emergency support</li>
                      <li>• Same-day response in major cities</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Europe</CardTitle>
                    <CardDescription>
                      Extensive network covering all major European markets
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 40+ service centers</li>
                      <li>• Multilingual support</li>
                      <li>• EU regulatory compliance</li>
                      <li>• Regional training centers</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Asia Pacific</CardTitle>
                    <CardDescription>
                      Growing presence in key Asian markets
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 25+ service centers</li>
                      <li>• Local language support</li>
                      <li>• Regional partnerships</li>
                      <li>• Rapid expansion program</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Latin America</CardTitle>
                    <CardDescription>
                      Strategic coverage in major Latin American countries
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 15+ service centers</li>
                      <li>• Spanish/Portuguese support</li>
                      <li>• Local distributor network</li>
                      <li>• Remote support capabilities</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Middle East & Africa</CardTitle>
                    <CardDescription>
                      Expanding presence in emerging markets
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 10+ service centers</li>
                      <li>• Regional partnerships</li>
                      <li>• Mobile service units</li>
                      <li>• Training initiatives</li>
                    </ul>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Remote Support</CardTitle>
                    <CardDescription>
                      Global remote monitoring and support capabilities
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 24/7 remote monitoring</li>
                      <li>• Predictive maintenance</li>
                      <li>• Virtual troubleshooting</li>
                      <li>• Global command center</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-8 bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Global Support Statistics</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600">150+</p>
                    <p className="text-gray-600 text-sm">Service Centers</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">500+</p>
                    <p className="text-gray-600 text-sm">Certified Technicians</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">50+</p>
                    <p className="text-gray-600 text-sm">Countries Served</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">99.9%</p>
                    <p className="text-gray-600 text-sm">Uptime Guarantee</p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default GlobalNetwork;
