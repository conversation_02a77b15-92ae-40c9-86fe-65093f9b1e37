
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Video } from 'lucide-react';

const VideoPage = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center mb-8">
            <div className="mr-4">
              <Video className="h-8 w-8 text-gray-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">Video Resources</h1>
          </div>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">Training Videos</h2>
              <p className="text-gray-600 mb-6 text-center">
                Comprehensive video library covering product demonstrations, training materials, 
                and educational content for healthcare professionals.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-200 h-48 flex items-center justify-center">
                    <Video className="h-12 w-12 text-gray-400" />
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Product Training Series</h3>
                    <p className="text-sm text-gray-600">Step-by-step guides for our medical devices</p>
                  </div>
                </div>
                
                <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-200 h-48 flex items-center justify-center">
                    <Video className="h-12 w-12 text-gray-400" />
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Clinical Applications</h3>
                    <p className="text-sm text-gray-600">Real-world usage scenarios and best practices</p>
                  </div>
                </div>
                
                <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-200 h-48 flex items-center justify-center">
                    <Video className="h-12 w-12 text-gray-400" />
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Maintenance & Care</h3>
                    <p className="text-sm text-gray-600">Equipment maintenance and troubleshooting</p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default VideoPage;
