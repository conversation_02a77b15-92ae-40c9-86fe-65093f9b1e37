
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Phone, Mail, MapPin, Clock } from 'lucide-react';

const ContactUs = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Contact Us</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Get in Touch</h2>
              <p className="text-gray-600 mb-6">
                Our team is here to help you with any questions about our products and services. 
                Contact us through any of the methods below.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg">Phone Support</CardTitle>
                    </div>
                    <CardDescription>
                      Speak directly with our support team
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-900 font-medium">+****************</p>
                    <p className="text-gray-600 text-sm mt-1">Available 24/7 for urgent issues</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg">Email Support</CardTitle>
                    </div>
                    <CardDescription>
                      Send us a detailed message
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-900 font-medium"><EMAIL></p>
                    <p className="text-gray-600 text-sm mt-1">Response within 24 hours</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg">Corporate Headquarters</CardTitle>
                    </div>
                    <CardDescription>
                      Visit our main office
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-900 font-medium">710 Medtronic Parkway</p>
                    <p className="text-gray-900 font-medium">Minneapolis, MN 55432</p>
                    <p className="text-gray-600 text-sm mt-1">United States</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      <CardTitle className="text-lg">Business Hours</CardTitle>
                    </div>
                    <CardDescription>
                      When we're available
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-900 font-medium">Monday - Friday: 8:00 AM - 6:00 PM</p>
                    <p className="text-gray-900 font-medium">Saturday: 9:00 AM - 3:00 PM</p>
                    <p className="text-gray-600 text-sm mt-1">Central Time Zone</p>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-8 text-center">
                <Button size="lg" className="mr-4">
                  Submit an Inquiry
                </Button>
                <Button variant="outline" size="lg">
                  Find Local Representative
                </Button>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ContactUs;
