
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Manual = () => {
  const handleDownload = (fileName: string) => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '#'; // In a real app, this would be the actual PDF URL
    link.download = fileName;
    link.click();
    
    // For demo purposes, show an alert
    alert(`Downloading ${fileName}...`);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">User Manuals</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">Equipment Manuals</h2>
              <p className="text-gray-600 mb-6 text-center">
                Access comprehensive user manuals, installation guides, and technical documentation 
                for all our medical equipment and systems.
              </p>
              
              <div className="grid grid-cols-1 gap-6">
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Ultrasound Systems</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">User Manual</h4>
                      <p className="text-sm text-gray-600 mb-3">Complete operating instructions and procedures</p>
                      <button 
                        onClick={() => handleDownload('Ultrasound_User_Manual.pdf')}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PDF
                      </button>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">Installation Guide</h4>
                      <p className="text-sm text-gray-600 mb-3">Step-by-step installation and setup instructions</p>
                      <button 
                        onClick={() => handleDownload('Ultrasound_Installation_Guide.pdf')}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PDF
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Digital Radiography</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">Operator Manual</h4>
                      <p className="text-sm text-gray-600 mb-3">Daily operation and maintenance procedures</p>
                      <button 
                        onClick={() => handleDownload('Digital_Radiography_Operator_Manual.pdf')}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PDF
                      </button>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">Service Manual</h4>
                      <p className="text-sm text-gray-600 mb-3">Technical service and troubleshooting guide</p>
                      <button 
                        onClick={() => handleDownload('Digital_Radiography_Service_Manual.pdf')}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PDF
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Computed Tomography</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">System Manual</h4>
                      <p className="text-sm text-gray-600 mb-3">Comprehensive system operation guide</p>
                      <button 
                        onClick={() => handleDownload('CT_System_Manual.pdf')}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PDF
                      </button>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">Safety Guidelines</h4>
                      <p className="text-sm text-gray-600 mb-3">Safety protocols and radiation protection</p>
                      <button 
                        onClick={() => handleDownload('CT_Safety_Guidelines.pdf')}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Download PDF
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">Quick Reference Guides</h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">Need Help?</h3>
                <p className="text-blue-800 mb-4">
                  Can't find the manual you're looking for? Contact our support team for assistance.
                </p>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                  Contact Support
                </button>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Manual;
