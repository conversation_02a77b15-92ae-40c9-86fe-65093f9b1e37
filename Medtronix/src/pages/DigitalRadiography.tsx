
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import DigitalRadiographyProductsSection from '@/components/digital-radiography/DigitalRadiographyProductsSection';

const DigitalRadiography = () => {
  const navigate = useNavigate();

  const handleScheduleConsultation = () => {
    navigate('/contact');
  };

  const handleDownloadBrochure = () => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '/placeholder.svg'; // Using placeholder as demo file
    link.download = 'Digital-Radiography-Brochure.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-6">Digital Radiography Solutions</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Advanced digital radiography systems delivering exceptional image quality, 
              workflow efficiency, and comprehensive diagnostic capabilities for modern healthcare environments.
            </p>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 text-2xl font-bold">DR</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Digital Excellence</h3>
              <p className="text-gray-600">
                State-of-the-art digital detectors providing superior image quality and diagnostic confidence.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 text-2xl font-bold">⚡</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Rapid Workflow</h3>
              <p className="text-gray-600">
                Streamlined imaging processes with fast acquisition and immediate image availability.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 text-2xl font-bold">📱</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Integration</h3>
              <p className="text-gray-600">
                Seamless integration with existing systems and advanced connectivity options.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <DigitalRadiographyProductsSection />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Ready to Upgrade Your Imaging?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Contact our digital radiography specialists to learn how our solutions can enhance your diagnostic capabilities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={handleScheduleConsultation}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Schedule Consultation
            </button>
            <button 
              onClick={handleDownloadBrochure}
              className="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Download Brochure
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default DigitalRadiography;
