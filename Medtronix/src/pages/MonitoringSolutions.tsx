
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import MonitoringHero from '@/components/monitoring-solutions/MonitoringHero';
import MonitoringOverview from '@/components/monitoring-solutions/MonitoringOverview';
import PatientMonitorsProductsSection from '@/components/monitoring-solutions/PatientMonitorsProductsSection';
import MonitoringContact from '@/components/monitoring-solutions/MonitoringContact';

const MonitoringSolutions = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <MonitoringHero onClose={() => setShowPage(false)} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 space-y-20">
        <MonitoringOverview />
        <PatientMonitorsProductsSection />
        <MonitoringContact />
      </main>

      <Footer />
    </div>
  );
};

export default MonitoringSolutions;
