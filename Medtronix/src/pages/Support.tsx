
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const Support = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-start">
          <div className="flex-1 max-w-md">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900">Support</h1>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowPage(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-8">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Service</h2>
                <div className="space-y-3">
                  <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Service Offerings
                  </a>
                  <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    RMS
                  </a>
                  <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Site Planning
                  </a>
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Contact Us</h2>
                <div className="space-y-3">
                  <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Inquiry
                  </a>
                  <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Global Network
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="ml-8 w-80">
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <img 
                src="https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?w=400&h=250&fit=crop" 
                alt="Professional support consultation" 
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <p className="text-gray-900 font-medium leading-relaxed">
                  Get in touch with us to get further information about our products and services...
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Support;
