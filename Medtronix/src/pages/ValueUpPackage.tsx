
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const ValueUpPackage = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Value-up Package</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">Enhanced Value Solutions</h2>
              <p className="text-gray-600 mb-6 text-center">
                Comprehensive value-added packages designed to maximize your equipment investment 
                and enhance operational efficiency with additional services and support.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Extended Warranty</h3>
                  <p className="text-gray-600">
                    Additional warranty coverage beyond standard terms to protect your investment and reduce unexpected costs.
                  </p>
                </div>
                
                <div className="bg-green-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Priority Support</h3>
                  <p className="text-gray-600">
                    Expedited technical support with reduced response times and dedicated service representatives.
                  </p>
                </div>
                
                <div className="bg-yellow-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Advanced Training</h3>
                  <p className="text-gray-600">
                    Comprehensive training programs including advanced techniques and best practices for optimal equipment use.
                  </p>
                </div>
                
                <div className="bg-purple-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Performance Analytics</h3>
                  <p className="text-gray-600">
                    Detailed analytics and reporting on equipment performance to optimize workflows and efficiency.
                  </p>
                </div>
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">Package Benefits</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-blue-600 text-2xl font-bold">💰</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Cost Savings</h3>
                  <p className="text-gray-600">
                    Bundled services at reduced rates compared to individual service purchases.
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-green-600 text-2xl font-bold">⚡</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Enhanced Performance</h3>
                  <p className="text-gray-600">
                    Optimized equipment performance through proactive maintenance and support.
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-purple-600 text-2xl font-bold">🛡️</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Peace of Mind</h3>
                  <p className="text-gray-600">
                    Comprehensive coverage and support for worry-free equipment operation.
                  </p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ValueUpPackage;
