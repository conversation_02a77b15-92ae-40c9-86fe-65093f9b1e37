
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const CatalogueSoftware = () => {
  const handleDownload = (itemName: string) => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '/placeholder.svg'; // Using placeholder as demo file
    link.download = `${itemName.replace(/\s+/g, '-')}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    alert(`Downloading ${itemName}...`);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Catalogue & Software</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Product Catalogues</h2>
              <p className="text-gray-600 mb-6">
                Browse our comprehensive product catalogues and download the latest software 
                for optimal equipment performance and functionality.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Digital Catalogues</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">Ultrasound Systems Catalogue</span>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleDownload('Ultrasound Systems Catalogue')}
                      >
                        Download
                      </button>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">Digital Radiography Catalogue</span>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleDownload('Digital Radiography Catalogue')}
                      >
                        Download
                      </button>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">CT Systems Catalogue</span>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleDownload('CT Systems Catalogue')}
                      >
                        Download
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Software Downloads</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">System Management Software</span>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleDownload('System Management Software')}
                      >
                        Download
                      </button>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">Image Processing Suite</span>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleDownload('Image Processing Suite')}
                      >
                        Download
                      </button>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <span className="font-medium">Diagnostic Tools Package</span>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => handleDownload('Diagnostic Tools Package')}
                      >
                        Download
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Software Requirements</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900 mb-3">System Requirements</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Windows 10 or later / macOS 10.15 or later</li>
                  <li>• Minimum 8GB RAM (16GB recommended)</li>
                  <li>• 500GB available storage space</li>
                  <li>• Network connectivity for updates and support</li>
                  <li>• Compatible graphics card for image processing</li>
                </ul>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CatalogueSoftware;
