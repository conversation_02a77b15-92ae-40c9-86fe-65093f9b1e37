
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const Resources = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-start">
            <div className="flex-1 max-w-md">
              <div className="flex justify-between items-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900">Resources</h1>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setShowPage(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-8">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Education</h2>
                  <div className="space-y-3">
                    <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                      theSUITE ↗
                    </a>
                    <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                      Video
                    </a>
                    <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                      Insight
                    </a>
                  </div>
                </div>

                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Download</h2>
                  <div className="space-y-3">
                    <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors border-b border-gray-300 pb-1">
                      Cleaning Guidelines
                    </a>
                    <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                      Catalogue & Software
                    </a>
                    <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                      Manual
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div className="ml-8 w-80">
              <div className="bg-white rounded-lg overflow-hidden shadow-sm">
                <img 
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop" 
                  alt="Medical professional using technology" 
                  className="w-full h-64 object-cover"
                />
                <div className="p-6">
                  <p className="text-gray-900 font-medium leading-relaxed">
                    Find compatible cleaning, disinfection & get products for your equipment...
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Resources;
