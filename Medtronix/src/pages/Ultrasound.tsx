
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import UltrasoundHero from '@/components/ultrasound/UltrasoundHero';
import UltrasoundOverview from '@/components/ultrasound/UltrasoundOverview';
import UltrasoundProductsSection from '@/components/ultrasound/UltrasoundProductsSection';
import UltrasoundContact from '@/components/ultrasound/UltrasoundContact';

const Ultrasound = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <UltrasoundHero onClose={() => setShowPage(false)} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 space-y-20">
        <UltrasoundOverview />
        <UltrasoundProductsSection />
        <UltrasoundContact />
      </main>

      <Footer />
    </div>
  );
};

export default Ultrasound;
