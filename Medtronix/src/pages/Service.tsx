
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Service = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Service</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Comprehensive Service Solutions</h2>
              <p className="text-gray-600 mb-6">
                We provide comprehensive service solutions to ensure your medical equipment operates 
                at peak performance throughout its lifecycle.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle>Service Offerings</CardTitle>
                    <CardDescription>
                      Explore our complete range of service solutions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Link to="/service-offerings">
                      <Button className="w-full">Learn More</Button>
                    </Link>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle>Remote Monitoring (RMS)</CardTitle>
                    <CardDescription>
                      24/7 remote monitoring and support services
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Link to="/rms">
                      <Button className="w-full">Learn More</Button>
                    </Link>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle>Site Planning</CardTitle>
                    <CardDescription>
                      Professional site planning and installation services
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Link to="/site-planning">
                      <Button className="w-full">Learn More</Button>
                    </Link>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle>Global Network</CardTitle>
                    <CardDescription>
                      Access our worldwide service network
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Link to="/global-network">
                      <Button className="w-full">Learn More</Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Service;
