
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const SitePlanning = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-4xl">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Site Planning</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Professional Site Planning Services</h2>
              <p className="text-gray-600 mb-6">
                Our expert site planning services ensure optimal equipment placement, workflow efficiency, 
                and compliance with all regulatory requirements for your medical facility.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Planning Process</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">1</div>
                      <div>
                        <h4 className="font-medium text-gray-900">Site Assessment</h4>
                        <p className="text-gray-600 text-sm">Comprehensive evaluation of your facility requirements</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">2</div>
                      <div>
                        <h4 className="font-medium text-gray-900">Design Development</h4>
                        <p className="text-gray-600 text-sm">Custom layout design optimized for workflow and safety</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">3</div>
                      <div>
                        <h4 className="font-medium text-gray-900">Implementation</h4>
                        <p className="text-gray-600 text-sm">Professional installation and commissioning</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Considerations</h3>
                  <ul className="space-y-3 text-gray-600">
                    <li>• Radiation shielding requirements</li>
                    <li>• HVAC and environmental controls</li>
                    <li>• Electrical power and backup systems</li>
                    <li>• Network infrastructure and connectivity</li>
                    <li>• Workflow optimization</li>
                    <li>• Regulatory compliance</li>
                    <li>• Future expansion planning</li>
                  </ul>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SitePlanning;
