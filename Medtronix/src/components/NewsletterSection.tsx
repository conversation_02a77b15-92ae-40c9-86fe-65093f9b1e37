
import { Button } from '@/components/ui/button';

const NewsletterSection = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Large photo */}
          <div className="relative">
            <img 
              src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=800&h=600&fit=crop" 
              alt="Medical professional using technology" 
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent rounded-lg"></div>
          </div>

          {/* Right side - Content and additional photos */}
          <div className="space-y-6">
            <div className="text-center lg:text-left">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Subscription</h2>
              <p className="text-lg text-gray-700 mb-8">
                Stay updated with the latest innovations in medical technology and clinical insights
              </p>
              <Button className="bg-black hover:bg-gray-800 text-white px-8 py-3 rounded-full text-lg">
                Subscribe Now
              </Button>
            </div>

            {/* Additional smaller photos grid */}
            <div className="grid grid-cols-2 gap-4 mt-8">
              <img 
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop" 
                alt="Medical equipment in use" 
                className="w-full h-48 object-cover rounded-lg shadow-md"
              />
              <img 
                src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=400&h=300&fit=crop" 
                alt="Healthcare environment" 
                className="w-full h-48 object-cover rounded-lg shadow-md"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection;
