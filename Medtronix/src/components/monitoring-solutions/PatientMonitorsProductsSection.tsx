
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

const subcategories = {
  patientMonitors: {
    title: "Patient Monitors",
    description: "Comprehensive patient monitoring solutions for critical care and general ward applications",
    products: [
      {
        name: "Brio X Series",
        image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop",
        description: "Advanced modular patient monitoring system with comprehensive vital signs monitoring and seamless connectivity.",
        detailedInfo: "The Brio X Series represents the pinnacle of patient monitoring technology with modular design, advanced parameter monitoring, intelligent alarm management, and comprehensive connectivity options. Features include high-resolution displays, wireless capabilities, and integration with hospital information systems for optimal clinical workflow."
      },
      {
        name: "BM7",
        image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop",
        description: "High-performance patient monitor designed for intensive care units and critical care environments.",
        detailedInfo: "BM7 delivers exceptional monitoring capabilities with advanced hemodynamic monitoring, multi-parameter display, and sophisticated alarm algorithms. Ideal for ICU applications requiring comprehensive patient monitoring with reliable performance and clinical decision support features."
      },
      {
        name: "BM5",
        image: "https://images.unsplash.com/photo-**********-a9333d879b1f?w=400&h=300&fit=crop",
        description: "Versatile patient monitor suitable for general ward and step-down unit applications.",
        detailedInfo: "BM5 offers reliable vital signs monitoring with essential parameters, user-friendly interface, and cost-effective solution for general ward monitoring. Features include basic arrhythmia detection, trend monitoring, and network connectivity for centralized monitoring systems."
      },
      {
        name: "BM3",
        image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=300&fit=crop",
        description: "Compact patient monitor for basic vital signs monitoring in various clinical settings.",
        detailedInfo: "BM3 provides essential patient monitoring capabilities in a compact design perfect for ambulatory care, post-operative monitoring, and general ward applications. Features streamlined interface, reliable measurements, and affordable monitoring solution without compromising quality."
      },
      {
        name: "BM1",
        image: "https://images.unsplash.com/photo-1586899028174-e7098604235b?w=400&h=300&fit=crop",
        description: "Entry-level patient monitor for basic vital signs monitoring and spot-check measurements.",
        detailedInfo: "BM1 delivers fundamental monitoring capabilities with essential vital signs parameters, simple operation, and reliable performance. Ideal for routine monitoring applications, outpatient settings, and environments requiring basic but dependable patient monitoring solutions."
      },
      {
        name: "BM Central",
        image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=300&fit=crop",
        description: "Centralized monitoring system for comprehensive patient surveillance across multiple units.",
        detailedInfo: "BM Central provides comprehensive central monitoring capabilities with multi-patient surveillance, advanced alarm management, and clinical information integration. Features include customizable displays, historical data analysis, and seamless integration with hospital information systems for enhanced clinical decision-making."
      },
      {
        name: "Oxy9Wave",
        image: "https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop",
        description: "Advanced pulse oximetry and capnography monitoring system for respiratory care applications.",
        detailedInfo: "Oxy9Wave delivers specialized respiratory monitoring with advanced pulse oximetry, capnography, and respiratory rate monitoring. Features include motion-resistant technology, low perfusion monitoring capabilities, and comprehensive respiratory assessment tools for critical care and anesthesia applications."
      }
    ]
  }
};

const PatientMonitorsProductsSection = () => {
  const handleDownloadBrochure = (productName: string) => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '/placeholder.svg'; // Using placeholder as demo file
    link.download = `${productName.replace(/\s+/g, '-')}-Brochure.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    alert(`Downloading ${productName} brochure...`);
  };

  return (
    <div className="mb-20">
      <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Products & Solutions by Category</h2>
      
      <Tabs defaultValue="patientMonitors" className="w-full">
        <TabsList className="grid w-full grid-cols-1 mb-8">
          <TabsTrigger value="patientMonitors">Patient Monitors</TabsTrigger>
        </TabsList>
        
        {Object.entries(subcategories).map(([key, category]) => (
          <TabsContent key={key} value={key} className="mt-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{category.title}</h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">{category.description}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {category.products.map((product, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="p-0">
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-full h-64 object-cover rounded-t-lg"
                    />
                  </CardHeader>
                  <CardContent className="p-6">
                    <CardTitle className="text-xl mb-3">{product.name}</CardTitle>
                    <CardDescription className="text-gray-600 leading-relaxed mb-4">
                      {product.description}
                    </CardDescription>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" className="w-full">
                          Learn More
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle className="text-2xl">{product.name}</DialogTitle>
                          <DialogDescription asChild>
                            <div className="space-y-4">
                              <img 
                                src={product.image} 
                                alt={product.name}
                                className="w-full h-48 object-cover rounded-lg"
                              />
                              <p className="text-base text-gray-700 leading-relaxed">
                                {product.detailedInfo}
                              </p>
                              <div className="flex gap-2 mt-6">
                                <Button className="flex-1">Contact Sales</Button>
                                <Button
                                  variant="outline"
                                  className="flex-1"
                                  onClick={() => handleDownloadBrochure(product.name)}
                                >
                                  Download Brochure
                                </Button>
                              </div>
                            </div>
                          </DialogDescription>
                        </DialogHeader>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default PatientMonitorsProductsSection;
