
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface MonitoringHeroProps {
  onClose: () => void;
}

const MonitoringHero = ({ onClose }: MonitoringHeroProps) => {
  return (
    <section className="relative bg-gradient-to-r from-green-50 to-emerald-50 py-20">
      <Button 
        variant="ghost" 
        size="sm" 
        className="absolute top-4 right-4 z-10"
        onClick={onClose}
      >
        <X className="h-4 w-4" />
      </Button>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-6">Patient Monitoring Solutions</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Advanced patient monitoring systems providing comprehensive vital signs monitoring, 
            enhanced patient safety, and seamless integration for optimal clinical workflows.
          </p>
        </div>
      </div>
    </section>
  );
};

export default MonitoringHero;
