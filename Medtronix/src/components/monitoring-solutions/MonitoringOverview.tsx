
const MonitoringOverview = () => {
  return (
    <section className="mb-20">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-green-600 text-2xl font-bold">📊</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Real-time Monitoring</h3>
          <p className="text-gray-600">
            Continuous vital signs monitoring with advanced alarm management and trend analysis.
          </p>
        </div>
        <div className="text-center">
          <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-blue-600 text-2xl font-bold">🔗</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Connectivity</h3>
          <p className="text-gray-600">
            Seamless integration with hospital systems and wireless connectivity options.
          </p>
        </div>
        <div className="text-center">
          <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-purple-600 text-2xl font-bold">🛡️</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Patient Safety</h3>
          <p className="text-gray-600">
            Enhanced patient safety with intelligent alarms and clinical decision support.
          </p>
        </div>
      </div>
    </section>
  );
};

export default MonitoringOverview;
