
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface UltrasoundHeroProps {
  onClose: () => void;
}

const UltrasoundHero = ({ onClose }: UltrasoundHeroProps) => {
  return (
    <section className="relative bg-gradient-to-r from-gray-800 to-gray-600 text-white py-20">
      <div className="absolute inset-0 opacity-30">
        <img 
          src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=1200&h=400&fit=crop" 
          alt="Ultrasound Equipment" 
          className="w-full h-full object-cover"
        />
      </div>
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-start">
          <div className="max-w-2xl">
            <h1 className="text-5xl font-bold mb-6">Ultrasound System</h1>
            <p className="text-xl leading-relaxed">
              Explore the latest innovations and empower your confidence 
              with Samsung Ultrasound systems designed for comprehensive medical imaging.
            </p>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            className="text-white hover:bg-white/20"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default UltrasoundHero;
