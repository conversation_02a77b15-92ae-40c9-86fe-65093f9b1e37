
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface ECGHeroProps {
  onClose: () => void;
}

const ECGHero = ({ onClose }: ECGHeroProps) => {
  return (
    <section className="relative bg-gradient-to-r from-blue-50 to-cyan-50 py-20">
      <Button 
        variant="ghost" 
        size="sm" 
        className="absolute top-4 right-4 z-10"
        onClick={onClose}
      >
        <X className="h-4 w-4" />
      </Button>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-6">ECG Solutions</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Advanced electrocardiography systems providing accurate cardiac monitoring, 
            comprehensive diagnostic capabilities, and seamless workflow integration for optimal patient care.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ECGHero;
