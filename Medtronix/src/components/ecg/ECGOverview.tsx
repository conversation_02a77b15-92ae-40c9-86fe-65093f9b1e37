
const ECGOverview = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div className="space-y-8">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Advanced ECG Technology</h2>
          <p className="text-lg text-gray-600 leading-relaxed mb-6">
            Our ECG systems deliver exceptional cardiac monitoring with high-precision signal acquisition, 
            advanced arrhythmia detection, and comprehensive diagnostic tools for various clinical environments.
          </p>
        </div>

        <div className="space-y-6">
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-blue-600 rounded-full mt-3"></div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">High-Precision Monitoring</h3>
              <p className="text-gray-600">Accurate cardiac signal acquisition for reliable diagnosis</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-blue-600 rounded-full mt-3"></div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Advanced Analytics</h3>
              <p className="text-gray-600">Comprehensive arrhythmia detection and cardiac analysis</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-blue-600 rounded-full mt-3"></div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Seamless Integration</h3>
              <p className="text-gray-600">Easy workflow integration and data management</p>
            </div>
          </div>
        </div>
      </div>

      <div className="relative">
        <img 
          src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=600&h=400&fit=crop" 
          alt="ECG System" 
          className="w-full h-96 object-cover rounded-lg shadow-lg"
        />
      </div>
    </div>
  );
};

export default ECGOverview;
