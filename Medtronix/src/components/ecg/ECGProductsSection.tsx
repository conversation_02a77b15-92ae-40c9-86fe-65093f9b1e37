
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

const products = [
  {
    name: "Cardio Q70",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop",
    description: "Advanced ECG system with comprehensive cardiac monitoring and analysis capabilities for clinical excellence.",
    detailedInfo: "The Cardio Q70 represents the pinnacle of ECG technology with advanced signal processing, comprehensive arrhythmia detection, and intuitive user interface. Features include high-resolution displays, wireless connectivity, and integration with hospital information systems for optimal clinical workflow."
  },
  {
    name: "Cardio Q50",
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop",
    description: "High-performance ECG monitor designed for intensive care and critical cardiac monitoring applications.",
    detailedInfo: "Cardio Q50 delivers exceptional cardiac monitoring capabilities with advanced signal acquisition, multi-lead ECG analysis, and sophisticated alarm algorithms. Ideal for ICU applications requiring comprehensive cardiac monitoring with reliable performance and clinical decision support."
  },
  {
    name: "Cardio P1",
    image: "https://images.unsplash.com/photo-**********-a9333d879b1f?w=400&h=300&fit=crop",
    description: "Portable ECG system suitable for ambulatory and point-of-care cardiac monitoring applications.",
    detailedInfo: "Cardio P1 offers reliable cardiac monitoring in a portable design perfect for ambulatory care, emergency medicine, and point-of-care applications. Features streamlined interface, battery operation, and wireless data transmission capabilities."
  },
  {
    name: "Cardio10",
    image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=400&h=300&fit=crop",
    description: "Compact ECG monitor for routine cardiac monitoring in various clinical settings.",
    detailedInfo: "Cardio10 provides essential cardiac monitoring capabilities in a compact design perfect for general ward monitoring, outpatient clinics, and routine cardiac assessments. Features user-friendly interface, reliable measurements, and cost-effective monitoring solution."
  },
  {
    name: "Cardio7",
    image: "https://images.unsplash.com/photo-1586899028174-e7098604235b?w=400&h=300&fit=crop",
    description: "Entry-level ECG system for basic cardiac monitoring and routine electrocardiography.",
    detailedInfo: "Cardio7 delivers fundamental cardiac monitoring capabilities with essential ECG parameters, simple operation, and reliable performance. Ideal for routine cardiac monitoring, primary care settings, and environments requiring basic but dependable ECG solutions."
  },
  {
    name: "Cardio7e",
    image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=300&fit=crop",
    description: "Enhanced version of Cardio7 with additional features and improved connectivity options.",
    detailedInfo: "Cardio7e provides enhanced cardiac monitoring capabilities with improved signal processing, additional analysis features, and enhanced connectivity options. Features include digital storage, basic arrhythmia detection, and network connectivity for data management."
  },
  {
    name: "CardioTOUCH 3000",
    image: "https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop",
    description: "Touch-screen ECG system with intuitive interface and advanced cardiac analysis capabilities.",
    detailedInfo: "CardioTOUCH 3000 delivers advanced cardiac monitoring with intuitive touch-screen interface, comprehensive ECG analysis, and advanced reporting capabilities. Features include customizable displays, automated measurements, and comprehensive cardiac assessment tools."
  },
  {
    name: "CardioCare 2000",
    image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=400&h=300&fit=crop",
    description: "Comprehensive cardiac care system with integrated monitoring and analysis features.",
    detailedInfo: "CardioCare 2000 provides comprehensive cardiac care capabilities with integrated monitoring, analysis, and documentation features. Ideal for cardiac care units requiring complete cardiac assessment and monitoring solutions with advanced clinical decision support."
  },
  {
    name: "BMS Plus",
    image: "https://images.unsplash.com/photo-**********-a9333d879b1f?w=400&h=300&fit=crop",
    description: "Biomedical monitoring system with multi-parameter cardiac and vital signs monitoring.",
    detailedInfo: "BMS Plus delivers comprehensive biomedical monitoring with multi-parameter cardiac monitoring, vital signs assessment, and integrated patient data management. Features include advanced alarm management, trend analysis, and comprehensive patient monitoring capabilities."
  },
  {
    name: "Spirocare",
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop",
    description: "Spirometry and cardiac monitoring system for comprehensive respiratory and cardiac assessment.",
    detailedInfo: "Spirocare provides specialized respiratory and cardiac monitoring capabilities with spirometry testing, lung function assessment, and cardiac monitoring integration. Features include comprehensive pulmonary function testing, cardiac rhythm analysis, and integrated reporting systems."
  }
];

const ECGProductsSection = () => {
  return (
    <div className="mb-20">
      <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">ECG Products & Solutions</h2>
      
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-4">ECG Systems</h3>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          Comprehensive electrocardiography solutions for accurate cardiac monitoring and diagnosis
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {products.map((product, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="p-0">
              <img 
                src={product.image} 
                alt={product.name}
                className="w-full h-64 object-cover rounded-t-lg"
              />
            </CardHeader>
            <CardContent className="p-6">
              <CardTitle className="text-xl mb-3">{product.name}</CardTitle>
              <CardDescription className="text-gray-600 leading-relaxed mb-4">
                {product.description}
              </CardDescription>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" className="w-full">
                    Learn More
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                    <DialogTitle className="text-2xl">{product.name}</DialogTitle>
                    <DialogDescription asChild>
                      <div className="space-y-4">
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        <p className="text-base text-gray-700 leading-relaxed">
                          {product.detailedInfo}
                        </p>
                        <div className="flex gap-2 mt-6">
                          <Button className="flex-1">Contact Sales</Button>
                          <Button variant="outline" className="flex-1">Download Brochure</Button>
                        </div>
                      </div>
                    </DialogDescription>
                  </DialogHeader>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ECGProductsSection;
