
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { Link } from 'react-router-dom';

interface CategoriesMenuProps {
  onClose: () => void;
}

const CategoriesMenu = ({ onClose }: CategoriesMenuProps) => {
  return (
    <div 
      className="absolute top-16 left-0 w-full bg-white shadow-lg z-50 border-t"
      onMouseLeave={onClose}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-start">
          <div className="flex-1 max-w-4xl">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">Medical Categories</h1>
            
            <div className="grid grid-cols-3 gap-8">
              <Link to="/womens-health" className="group">
                <div className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                  <img 
                    src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=300&h=200&fit=crop" 
                    alt="Women's Health" 
                    className="w-full h-32 object-cover rounded mb-4"
                  />
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">Women's Health</h3>
                  <p className="text-sm text-gray-600 mt-2">Specialized imaging for women's healthcare</p>
                </div>
              </Link>

              <Link to="/point-of-care" className="group">
                <div className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                  <img 
                    src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop" 
                    alt="Point of Care" 
                    className="w-full h-32 object-cover rounded mb-4"
                  />
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">Point of Care</h3>
                  <p className="text-sm text-gray-600 mt-2">Portable bedside diagnostic solutions</p>
                </div>
              </Link>

              <Link to="/cardiology" className="group">
                <div className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                  <img 
                    src="https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=300&h=200&fit=crop" 
                    alt="Cardiology" 
                    className="w-full h-32 object-cover rounded mb-4"
                  />
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">Cardiology</h3>
                  <p className="text-sm text-gray-600 mt-2">Advanced cardiovascular imaging</p>
                </div>
              </Link>
            </div>
          </div>

          <Button 
            variant="ghost" 
            size="sm" 
            className="absolute top-4 right-4"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CategoriesMenu;
