
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ResourcesMenuProps {
  onClose: () => void;
}

const ResourcesMenu = ({ onClose }: ResourcesMenuProps) => {
  return (
    <div 
      className="absolute top-16 left-0 w-full bg-white shadow-lg z-50 border-t"
      onMouseLeave={onClose}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-start">
          <div className="flex-1 max-w-md">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">Resources</h1>

            <div className="space-y-8">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Education</h2>
                <div className="space-y-3">
                  <Link to="/education" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Education
                  </Link>
                  <Link to="/video" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Video
                  </Link>
                  <Link to="/insight" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Insight
                  </Link>
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Download</h2>
                <div className="space-y-3">
                  <Link to="/download" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Download
                  </Link>
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Support & Services</h2>
                <div className="space-y-3">
                  <Link to="/cyber-security" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Cyber Security
                  </Link>
                  <Link to="/value-up-package" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Value-up Package
                  </Link>
                  <Link to="/cleaning-guidelines" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Cleaning Guidelines
                  </Link>
                  <Link to="/manual" className="block text-gray-600 hover:text-gray-900 transition-colors">
                    Manual
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div className="ml-8 w-80">
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <img 
                src="https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?w=400&h=250&fit=crop" 
                alt="Medical resources and education" 
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <p className="text-gray-900 font-medium leading-relaxed">
                  Access our comprehensive resources including educational materials, technical documentation, and support services...
                </p>
              </div>
            </div>
          </div>

          <Button 
            variant="ghost" 
            size="sm" 
            className="absolute top-4 right-4"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ResourcesMenu;
