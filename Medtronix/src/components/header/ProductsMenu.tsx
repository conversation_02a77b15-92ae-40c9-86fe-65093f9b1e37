import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ProductsMenuProps {
  onClose: () => void;
}

const ProductsMenu = ({ onClose }: ProductsMenuProps) => {
  return (
    <div 
      className="absolute top-16 left-0 w-full bg-white shadow-lg z-50 border-t"
      onMouseLeave={onClose}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between">
          <div className="flex-1 grid grid-cols-4 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Imaging Systems</h3>
              <div className="space-y-2">
                <Link to="/ultrasound" className="block text-gray-600 hover:text-gray-900">Ultrasound System</Link>
                <Link to="/digital-radiography" className="block text-gray-600 hover:text-gray-900">Digital Radiography</Link>
                <Link to="/computed-tomography" className="block text-gray-600 hover:text-gray-900">Computed Tomography</Link>
                <a href="#" className="block text-gray-600 hover:text-gray-900">General Imaging</a>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Specialized Care</h3>
              <div className="space-y-2">
                <Link to="/cardiology" className="block text-gray-600 hover:text-gray-900">Cardiovascular</Link>
                <Link to="/womens-health" className="block text-gray-600 hover:text-gray-900">Women's Health</Link>
                <Link to="/point-of-care" className="block text-gray-600 hover:text-gray-900">Point of Care</Link>
                <a href="#" className="block text-gray-600 hover:text-gray-900">Pediatrics</a>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Monitoring Solutions</h3>
              <div className="space-y-2">
                <Link to="/monitoring-solutions" className="block text-gray-600 hover:text-gray-900">Patient Monitors</Link>
                <Link to="/ecg" className="block text-gray-600 hover:text-gray-900">ECG</Link>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Support & Services</h3>
              <div className="space-y-2">
                <a href="#" className="block text-gray-600 hover:text-gray-900">Cyber Security</a>
                <a href="#" className="block text-gray-600 hover:text-gray-900">Value-up Package</a>
                <a href="#" className="block text-gray-600 hover:text-gray-900">Cleaning Guidelines</a>
                <a href="#" className="block text-gray-600 hover:text-gray-900">Manual</a>
              </div>
            </div>
          </div>

          <div className="ml-8">
            <div className="bg-gray-100 rounded-lg p-6 w-80">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <img src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=150&h=100&fit=crop" alt="Ultrasound" className="rounded" />
                <img src="https://images.unsplash.com/photo-1518770660439-4636190af475?w=150&h=100&fit=crop" alt="Digital Radiography" className="rounded" />
                <img src="https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=150&h=100&fit=crop" alt="Cardiovascular" className="rounded" />
                <img src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=150&h=100&fit=crop" alt="Point of Care" className="rounded" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Relentless innovation for your diagnostic confidence</h4>
            </div>
          </div>

          <Button 
            variant="ghost" 
            size="sm" 
            className="absolute top-4 right-4"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProductsMenu;
