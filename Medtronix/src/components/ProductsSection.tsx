
import { Link } from 'react-router-dom';

const ProductsSection = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl font-bold text-center text-gray-900 mb-12">Products & Solutions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Main product cards */}
          <Link to="/cardiology" className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=200&h=150&fit=crop" 
                alt="Cardiovascular" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Cardiovascular</h3>
            <p className="text-sm text-gray-600 mt-2">Advanced cardiovascular imaging solutions</p>
          </Link>

          <Link to="/ultrasound" className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=200&h=150&fit=crop" 
                alt="Ultrasound System" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Ultrasound System</h3>
            <p className="text-sm text-gray-600 mt-2">High-resolution ultrasound imaging</p>
          </Link>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="/lovable-uploads/e4a1f2a1-33dc-4b92-980b-5e6e6f0aceef.png" 
                alt="General Imaging" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">General Imaging</h3>
            <p className="text-sm text-gray-600 mt-2">Comprehensive diagnostic imaging solutions</p>
          </div>

          <Link to="/digital-radiography" className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1518770660439-4636190af475?w=200&h=150&fit=crop" 
                alt="Digital Radiography" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Digital Radiography</h3>
            <p className="text-sm text-gray-600 mt-2">Advanced digital X-ray systems</p>
          </Link>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=200&h=150&fit=crop" 
                alt="Pediatrics" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Pediatrics</h3>
            <p className="text-sm text-gray-600 mt-2">Specialized pediatric imaging solutions</p>
          </div>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?w=200&h=150&fit=crop" 
                alt="Patient Monitors" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Patient Monitors</h3>
            <p className="text-sm text-gray-600 mt-2">Advanced patient monitoring systems</p>
          </div>

          <Link to="/point-of-care" className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=200&h=150&fit=crop" 
                alt="Point of Care" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Point of Care</h3>
            <p className="text-sm text-gray-600 mt-2">Portable bedside diagnostic solutions</p>
          </Link>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=200&h=150&fit=crop" 
                alt="ECG" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">ECG</h3>
            <p className="text-sm text-gray-600 mt-2">Electrocardiography monitoring solutions</p>
          </div>

          <Link to="/womens-health" className="bg-white rounded-lg p-6 text-center shadow-sm hover:shadow-md transition-shadow">
            <div className="mb-6">
              <img 
                src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=200&h=150&fit=crop" 
                alt="Women's Health" 
                className="w-full h-32 object-cover rounded mb-4"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Women's Health</h3>
            <p className="text-sm text-gray-600 mt-2">Specialized women's healthcare imaging</p>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
